import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { ChevronDown, Search } from 'lucide-react';
import { useTranslations } from 'next-intl';

type Option = {
  value: any;
  label: string;
};

type SearchableSelectProps = {
  options: Option[];
  value?: any;
  onChange?: (value: string) => void;
  onSearchChange?: (query: string) => void;
  placeholder?: string;
  searchPlaceholder?: string;
  disabled?: boolean;
  loading?: boolean;
  className?: string;
  searchValue?: string;
  positionView?: string;
};

const SearchableSelect: React.FC<SearchableSelectProps> = ({
  options,
  value = null,
  onChange,
  onSearchChange,
  placeholder = 'Select option',
  searchPlaceholder = 'Search...',
  disabled = false,
  loading = false,
  className = '',
  searchValue = '',
  positionView,
}) => {
  const t = useTranslations('workflow');

  const [isOpen, setIsOpen] = useState(false);
  const [internalSearchValue, setInternalSearchValue] = useState('');
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Use external search value if provided, otherwise use internal
  const currentSearchValue = searchValue || internalSearchValue;

  // Filter options based on search
  const filteredOptions = useMemo(() => {
    if (!currentSearchValue) {
      return options;
    }
    return options.filter(option =>
      option.label.toLowerCase().includes(currentSearchValue.toLowerCase()),
    );
  }, [options, currentSearchValue]);

  // Get selected option label
  const selectedOption = options.find(option => option.value === value);
  const displayValue = selectedOption?.label ?? value?.name ?? value ?? '';

  // Handle search change
  const handleSearchChange = useCallback((searchQuery: string) => {
    if (onSearchChange) {
      onSearchChange(searchQuery);
    } else {
      setInternalSearchValue(searchQuery);
    }
  }, [onSearchChange]);

  // Handle option selection
  const handleSelect = useCallback((optionValue: string) => {
    if (onChange) {
      onChange(optionValue);
    }
    setIsOpen(false);
    setInternalSearchValue('');
    if (onSearchChange) {
      onSearchChange('');
    }
  }, [onChange, onSearchChange]);

  // Handle dropdown toggle
  const toggleDropdown = useCallback(() => {
    if (disabled || loading) {
      return;
    }
    setIsOpen(prev => !prev);
    if (!isOpen) {
      // Focus search input when opening
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 0);
    }
  }, [disabled, loading, isOpen]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <div className={`relative ${className}`} ref={dropdownRef}>
      {/* Select trigger */}
      <div
        className={`h-11 w-full flex items-center justify-between rounded-lg border border-input px-4 py-2.5 text-sm shadow-theme-xs bg-background cursor-pointer focus:outline-none focus:ring-3 focus:ring-primary/10 ${disabled ? 'opacity-50 cursor-not-allowed' : ''
        } ${displayValue ? 'text-foreground' : 'text-muted-foreground'}`}
        onClick={toggleDropdown}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            toggleDropdown();
          }
        }}
      >
        <span className="truncate text-black">
          {loading ? 'Loading...' : displayValue || placeholder}
        </span>
        <ChevronDown className={`h-4 w-4 text-muted-foreground transition-transform ${isOpen ? 'rotate-180' : ''}`} />
      </div>

      {/* Dropdown */}
      {isOpen && (
        <div className={`${positionView || 'absolute'} z-50 w-full mt-1 bg-background border border-input rounded-lg shadow-lg max-h-60 overflow-hidden`}>
          {/* Search input */}
          <div className="sticky top-0 p-2 bg-background border-b border-border">
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <input
                ref={searchInputRef}
                type="text"
                placeholder={searchPlaceholder}
                value={currentSearchValue}
                onChange={e => handleSearchChange(e.target.value)}
                className="w-full pl-8 pr-2 py-2 text-sm border border-input rounded-md focus:outline-none focus:ring-2 focus:ring-primary/70"
                onClick={e => e.stopPropagation()}
              />
            </div>
          </div>

          {/* Options list */}
          <div className="max-h-48 overflow-y-auto">
            {loading
              ? (
                  <div className="p-4 text-center text-muted-foreground">{t('common.loading')}</div>
                )
              : filteredOptions.length === 0
                ? (
                    <div className="p-4 text-center text-muted-foreground">{t('businessForm.emptyMessage')}</div>
                  )
                : (
                    filteredOptions.map((option, indx) => (
                      <div
                        key={option.value?.id ?? indx ?? ''}
                        className={`px-4 py-2 cursor-pointer hover:bg-primary/5 text-sm ${option.value === value ? 'bg-primary/10 text-primary' : 'text-foreground'
                        }`}
                        onClick={() => handleSelect(option.value)}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault();
                            handleSelect(option.value);
                          }
                        }}
                      >
                        {option.label}
                      </div>
                    ))
                  )}
          </div>
        </div>
      )}
    </div>
  );
};

export default SearchableSelect;
