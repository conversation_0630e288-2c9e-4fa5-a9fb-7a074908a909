'use client';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/shared/components/ui/table';
import React, { useEffect, useState } from 'react';
import { SkeletonTable } from './SkeletonTable';
import { ChevronDownIcon, PlusCircleIcon, ShowMoreIcon } from '@/shared/icons';
import TableTemplates from './TableTemplate';
import type { ItemFrameworkResponse, TemplateItemFramework } from '@/features/frameworks-templates/types';
import { useFrameworkGet } from '@/features/frameworks-templates/hooks/useFrameworkGet';
import { useFrameworkFilters } from '@/features/frameworks-templates/hooks/useFrameworkFilters';
import { dateToDDMMYYHHMM } from '@/shared/utils/date';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import DeleteModalButton from '../../companies/modals/DeleteModalButton';
import { useFrameworkDelete } from '@/features/frameworks-templates/hooks/useFrameworkDelete';
import { toast } from 'sonner';
import TemplateForm from '../modals/TemplatesForm';
import { Env } from '@/core/config/Env';
import { useTemplateDelete } from '@/features/frameworks-templates/hooks/useTemplateDelete';
import { useTemplateCreate } from '@/features/frameworks-templates/hooks/useTemplateCreate';
import Pagination from '@/shared/components/tables/Pagination';
import { useTranslations } from 'next-intl';

export type TableConfig = {
  tag: string;
  label: string;
  className?: string;
  minWidth?: string;
  maxWidth?: string;
  type: 'file' | 'dropdown' | 'text' | 'no-view' | 'action';
};

type TemplateList = {
  id: string;
  template: string;
  isShowMore: boolean;
  isEdit: boolean;
  file: any;
  reportFile: any;
  status: string;
};

type FrameworkTableData = {
  id: string;
  no: number;
  framework: string;
  updatedAt: string;
  isShowMore: boolean;
  templateList: TemplateList[];
  [key: string]: string | boolean | number | TemplateList[];
};

export type TemplateListTable = {
  id: string;
  template: string;
  isShowMore: boolean;
  isEdit: boolean;
  status: string;
  file: {
    url: string;
    name: string;
    [key: string]: any;
  };
  reportFile: {
    url: string;
    name: string;
    [key: string]: any;
  };
};

type TableManagementType = {
  handleCreateFramework: () => void;
};

const TableManagement: React.FC<TableManagementType> = ({ handleCreateFramework }) => {
  const { apiFilters, currentPage, updatePage } = useFrameworkFilters();
  const { data, isLoading } = useFrameworkGet(apiFilters);

  const t = useTranslations('framework');
  const tableConfig: TableConfig[] = [
    {
      tag: 'no',
      label: t('no'),
      type: 'no-view',
    },
    {
      tag: 'framework',
      label: t('researchType'),
      type: 'text',
    },
    {
      tag: 'updatedAt',
      label: t('lastUpdated'),
      type: 'text',
    },
    {
      tag: 'actions',
      label: '',
      className: 'w-24',
      type: 'action',
    },
  ];

  const tableConfigTemplate: TableConfig[] = [
    {
      tag: 'no',
      label: '',
      type: 'no-view',
    },
    {
      tag: 'template',
      label: t('template'),
      type: 'no-view',
      minWidth: 'min-w-80',
    },
    // {
    //   tag: 'step',
    //   label: 'Step',
    // },
    {
      tag: 'file',
      label: t('questionnaire'),
      type: 'file',
      maxWidth: 'max-w-30',
    },
    {
      tag: 'reportFile',
      label: t('report'),
      type: 'file',
      maxWidth: 'max-w-30',
    },
    {
      tag: 'actions',
      label: '',
      className: 'w-24',
      type: 'action',
    },
  ];

  const [frameworkList, setFrameworkList] = useState<FrameworkTableData[]>([]);

  const [openPopoverId, setOpenPopoverId] = useState<string | null>(null);

  const [isOpenDeleteConfirm, setIsOpenDeleteConfirm] = useState<boolean>(false);

  const [isOpenForm, setIsOpenForm] = useState<boolean>(false);

  const [itemIdSelected, setItemIdSelected] = useState<string>('');

  const [templateListSelected, setTemplateListSelected] = useState<TemplateListTable[]>([]);

  const { mutateAsync: deleteFramework } = useFrameworkDelete();

  const { mutateAsync: deleteTemplate } = useTemplateDelete();

  const { mutateAsync: createTemplate } = useTemplateCreate();

  const toggleShowMore = (itemId: string) => {
    setFrameworkList(prevList =>
      prevList.map(item =>
        item.id === itemId
          ? { ...item, isShowMore: !item.isShowMore }
          : item,
      ),
    );
  };

  const handlePopoverOpenChange = (itemId: string, isOpen: boolean) => {
    setOpenPopoverId(isOpen ? itemId : null);
  };

  const initTemplateList = (templates: TemplateItemFramework[]) => {
    return templates.map((template) => {
      const file = template.files.find(f => f.category === 'questionnaire');
      const reportFile = template.files.find(f => f.category === 'report');
      return ({
        id: template.id,
        template: template.name,
        isShowMore: false,
        isEdit: false,
        status: 'old',
        file: {
          ...file?.file,
          url: file ? `${Env.NEXT_PUBLIC_API_SERVER}/public/${file.file.key}` : '',
          name: file ? file.file.originalname : '',
        },
        reportFile: {
          ...reportFile?.file,
          url: reportFile ? `${Env.NEXT_PUBLIC_API_SERVER}/public/${reportFile.file.key}` : '',
          name: reportFile ? reportFile.file.originalname : '',
        },
      });
    });
  };

  const initDataTable = (data: ItemFrameworkResponse[], currentPage: number, itemsPerPage: number = 10) => {
    return data.map((item, index) => ({
      no: (currentPage - 1) * itemsPerPage + index + 1,
      framework: item.name,
      updatedAt: dateToDDMMYYHHMM(item.updatedAt),
      isShowMore: false,
      id: item.id,
      templateList: initTemplateList(item.templates),
    }));
  };

  const openEditFramework = () => {
    setIsOpenForm(true);
  };

  const handleOpenModal = (itemId: string, action: 'edit' | 'delete', templateList?: TemplateListTable[]) => {
    setOpenPopoverId(null);

    setItemIdSelected(itemId);
    if (action === 'edit') {
      setTemplateListSelected(templateList ?? []);
      openEditFramework();
    } else if (action === 'delete') {
      setIsOpenDeleteConfirm(true);
    }
  };

  const handleCloseDeleteConfirm = () => {
    setIsOpenDeleteConfirm(false);
  };

  const handleCloseModalForm = () => {
    setIsOpenForm(false);
    setTemplateListSelected([]);
  };

  const handleConfirmDelete = async () => {
    try {
      await deleteFramework(itemIdSelected);
      setItemIdSelected('');
      handleCloseDeleteConfirm();

      toast.success(t('deleteMessage'));
    } catch (error: any) {
      toast.error(error.message);
    }
  };

  const handleSaveTemplate = (_index: number, template: any) => {
    const payload = {
      id: template.id,
      types: [
        template.file.name && {
          [template.file.name]: template.file.mimetype,
          category: 'questionnaire',
        },
        template.reportFile.name && {
          [template.reportFile.name]: template.reportFile.mimetype,
          category: 'report',
        },
      ],
      fileIds: [
        template.reportFile.name
        && template.reportFile._id,

        template.file.name
        && template.file._id,

      ],
      type: 'framework',
      name: template.template,
    };

    createTemplate(payload);
  };

  const handleDeleteTemplate = (_index: number, item: any, indexParent: number) => {
    deleteTemplate(item.id);

    setFrameworkList((prev) => {
      const newList = [...prev];
      const targetFramework = { ...newList[indexParent] };

      targetFramework.templateList = targetFramework!.templateList!.filter((_: any, i: number) => i !== _index);

      newList[indexParent] = targetFramework as FrameworkTableData;

      return newList;
    });
  };

  useEffect(() => {
    if (data) {
      const { items, itemsPerPage } = data;
      const frameworkList = initDataTable(items, currentPage, itemsPerPage);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setFrameworkList(frameworkList);
    }
  }, [data, currentPage]);

  return (
    <React.Fragment>
      <Table className="border border-solid rounded-xl">
        <TableHeader className="bg-gray-50 rounded-t-xl">
          <TableRow>
            {tableConfig.map(config => (
              <TableHead key={config.tag} className={config?.className}>
                {config.label}
              </TableHead>
            ))}
          </TableRow>
        </TableHeader>
        <TableBody>
          {isLoading
            ? <SkeletonTable tableConfig={tableConfig} />
            : frameworkList.length
              ? (
                  frameworkList.map((item, index) => (
                    <React.Fragment key={item.id || index}>
                      <TableRow>
                        {tableConfig.map((config, i) => (
                          config.tag !== 'actions'
                            ? (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  {item[config.tag] as (string | number)}
                                </TableCell>
                              )
                            : (
                                <TableCell key={config.tag + i} className={config?.className}>
                                  <div className="flex items-center gap-2">
                                    <Popover open={openPopoverId === item.id} onOpenChange={isOpen => handlePopoverOpenChange(item.id, isOpen)}>
                                      <PopoverTrigger>
                                        <ShowMoreIcon className="h-5 w-5 cursor-pointer" />
                                      </PopoverTrigger>
                                      <PopoverContent className="w-35 p-2">
                                        <div className="">
                                          <div onClick={() => handleOpenModal(item.id, 'edit', item.templateList)} className="p-1 cursor-pointer text-sm hover:bg-gray-50">{t('common.edit')}</div>
                                          <div onClick={() => handleOpenModal(item.id, 'delete')} className="p-1 cursor-pointer text-sm text-red-500 hover:bg-gray-100">{t('common.delete')}</div>
                                        </div>
                                      </PopoverContent>
                                    </Popover>
                                    <ChevronDownIcon onClick={() => toggleShowMore(item.id)} className="h-5 w-5 cursor-pointer" />
                                  </div>
                                </TableCell>
                              )
                        ))}

                      </TableRow>
                      {
                        item.isShowMore && (
                          <TableRow>
                            <TableCell colSpan={tableConfig.length}>
                              <div className="w-full">
                                <TableTemplates
                                  tableConfig={tableConfigTemplate}
                                  templateList={item.templateList}
                                  onSaveTemplate={handleSaveTemplate}
                                  deleteTemplate={(indexChild, item) => handleDeleteTemplate(indexChild, item, index)}
                                />
                              </div>
                            </TableCell>
                          </TableRow>
                        )
                      }
                    </React.Fragment>
                  ))
                )
              : (
                  <TableRow>
                    <TableCell className="bg-gray-50" colSpan={tableConfig.length} onClick={handleCreateFramework}>
                      <div className="min-h-[202px] w-full bg-gray-50 flex items-center justify-center">
                        <div className="cursor-pointer">
                          <div className="flex flex-col items-center justify-center gap-2">
                            <PlusCircleIcon className="h-6 w-6" />
                            <h3 className="text-lg">{t('create')}</h3>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
        </TableBody>
      </Table>

      {/* Pagination */}
      {data && data.totalPages > 1 && (
        <div className="mt-4 flex justify-center">
          <Pagination
            currentPage={currentPage}
            totalPages={data.totalPages}
            onPageChange={updatePage}
          />
        </div>
      )}

      <TemplateForm isOpen={isOpenForm} id={itemIdSelected} templates={templateListSelected ?? []} closeModal={handleCloseModalForm} />

      <DeleteModalButton
        isOpen={isOpenDeleteConfirm}
        message={t('messageModal')}
        onConfirm={handleConfirmDelete}
        closeModal={handleCloseDeleteConfirm}
      />
    </React.Fragment>
  );
};

export default TableManagement;
