'use client';

import type { Project } from '../../../types/project';
import { format } from 'date-fns';
import { useTranslations } from 'next-intl';
import Link from 'next/link';
import React, { useState } from 'react';
import { Button } from '@/shared/components/ui/button';
import { Card, CardContent } from '@/shared/components/ui/card';
import { Modal } from '@/shared/components/ui/modal';
import { Popover, PopoverContent, PopoverTrigger } from '@/shared/components/ui/popover';
import { ArrowTopRightOnSquareIcon, BriefcaseIcon, CalendarIcon, CircleCheckIcon, InformationCircleIcon, MoreDotIcon, TrashIcon, UserIcon } from '@/shared/icons';
import { cn } from '@/shared/utils/utils';

import { PROJECT_CAMPAIGN_LABEL, PROJECT_STATUS_LABEL, PROJECT_TYPE_LABEL } from '../../../constants';
import { useProjectDelete } from '../../../hooks';
import { ProjectCampaignEnum, ProjectStatusEnum, ProjectTypeEnum } from '../../../types/project';
import {
  projectCardCategoryVariants,
  projectCardContainerVariants,
  projectCardIconVariants,
  projectCardMetadataContainerVariants,
  projectCardMetadataItemVariants,
  projectCardStatusVariants,
} from './project-card-variants';
import { PermissionGuard } from '@/core/rbac/PermissionGuard';
import { Permission } from '@/core/rbac/permissions';
import { useProjectUpdateStatus } from '@/features/project-management/hooks/useProjectUpdateStatus';
import ProjectFromModal from '../modals/ProjectForm';
import { useModal } from '@/shared/hooks/useModal';

type ProjectCardProps = {
  project: Project;
};

const formatDate = (dateString: string) => {
  try {
    return format(new Date(dateString), 'MMM d, yyyy');
  } catch {
    return dateString;
  }
};

// Calculate progress based on status
// const calculateProgress = (status: ProjectStatusEnum): number => {
//   switch (status) {
//     case ProjectStatusEnum.PLANNED: // PLANNED
//       return 10;
//     case ProjectStatusEnum.IN_PROGRESS: // IN_PROGRESS
//       return 50;
//     case ProjectStatusEnum.COMPLETED: // COMPLETED
//       return 100;
//     case ProjectStatusEnum.ON_HOLD: // ON_HOLD
//       return 30;
//     default:
//       return 0;
//   }
// };

export default function ProjectCard({ project }: ProjectCardProps) {
  const {
    id,
    name,
    type,
    campaign,
    status,
    startDate,
    endDate,
    clientName,
    industry = 'Technology',
  } = project;

  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const { deleteProject, isDeleting } = useProjectDelete();

  const { updateStatusProject, isUpdating } = useProjectUpdateStatus();

  const { isOpen, openModal, closeModal } = useModal();

  const [projectSelected, setProjectSelected] = useState<Project | null>(null);

  const [showConfirmModal, setShowConfirmModal] = useState(false);

  // Calculate progress based on status
  // const progress = calculateProgress(status);

  const t = useTranslations('Project');

  const mainTranslate = useTranslations('main');

  const openDeleteConfirm = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation(); // Prevent event bubbling
    setShowDeleteConfirm(true);
  };

  const openConfirmCompleteProject = (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation(); // Prevent event bubbling
    setShowConfirmModal(true);
  };

  const closeDeleteConfirm = () => {
    setShowDeleteConfirm(false);
  };

  const handleShowInfoProject = (e: React.MouseEvent, project: Project) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation(); // Prevent event bubbling
    setProjectSelected(project);
    openModal();
  };

  const handleComplete = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation(); // Prevent event bubbling

    try {
      await updateStatusProject({ id, status: 2 });
      // Success is already handled in the hook with toast
      setShowConfirmModal(false);
    } catch (err) {
      // Error is already handled in the hook with toast
      console.error('Error while updating project', err);
    }
  };

  // Handle delete action
  const handleDelete = async (e: React.MouseEvent) => {
    e.preventDefault(); // Prevent navigation
    e.stopPropagation(); // Prevent event bubbling

    try {
      await deleteProject(id);
      // Success is already handled in the hook with toast
      closeDeleteConfirm();
    } catch (err) {
      // Error is already handled in the hook with toast
      console.error('Error while deleteing project', err);
      closeDeleteConfirm();
    }
  };

  return (
    <>
      <div className={cn(projectCardContainerVariants())}>
        <Link href={`/dashboard/projects/${id}`}>
          <Card
            className="group overflow-hidden shadow-none transition-all duration-300 h-full relative"
          >

            <CardContent className="px-5">
              {/* Header with title and status */}
              <div className="flex justify-between items-start gap-3 mb-4">
                <div className="overflow-hidden">
                  <h5 className="line-clamp-2">{name}</h5>

                  {/* Category and Type */}
                  <div className="flex items-center">
                    <span className={cn(projectCardCategoryVariants({ type }))}>
                      {(t(PROJECT_TYPE_LABEL[type ?? ProjectTypeEnum.BRANDING]))}
                    </span>

                    <span className="mx-2 text-border">•</span>

                    <span className="text-xs text-muted-foreground truncate">
                      {t(PROJECT_CAMPAIGN_LABEL[campaign ?? ProjectCampaignEnum.CORPORATE])}
                    </span>
                  </div>
                </div>

                <span className={cn(projectCardStatusVariants({ status }))}>
                  {t(PROJECT_STATUS_LABEL[status ?? ProjectStatusEnum.PLANNED])}
                </span>
              </div>

              {/* Progress bar */}
              {/* <div className="mb-5">
                <div className="flex justify-between text-xs mb-1.5">
                  <span className="text-muted-foreground font-medium">
                    {progress}
                    % completed
                  </span>
                </div>
                <div className={cn(projectCardProgressBarVariants())}>
                  <div
                    className={cn(projectCardProgressFillVariants({ status }))}
                    style={{
                      width: `${progress}%`,
                    }}
                  />
                </div>
              </div> */}

              {/* Metadata */}
              <div className={cn(projectCardMetadataContainerVariants())}>
                <div className={cn(projectCardMetadataItemVariants())}>
                  <CalendarIcon className={cn(projectCardIconVariants())} />
                  <span className="truncate">
                    {formatDate(startDate)}
                    {endDate && (
                      <>
                        {' '}
                        -
                        {' '}
                        {formatDate(endDate)}
                      </>
                    )}
                  </span>
                </div>

                <div className={cn(projectCardMetadataItemVariants())}>
                  <UserIcon className={cn(projectCardIconVariants())} />
                  <span className="truncate">
                    {project.memberIds?.length || 0}
                    {' '}
                    {mainTranslate('member')}
                    {project.memberIds?.length !== 1 ? 's' : ''}
                  </span>
                </div>

                <div className={cn(projectCardMetadataItemVariants())}>
                  <BriefcaseIcon className={cn(projectCardIconVariants())} />
                  <span className="truncate">{clientName}</span>
                </div>

                <div className={cn(projectCardMetadataItemVariants())}>
                  <ArrowTopRightOnSquareIcon className={cn(projectCardIconVariants())} />
                  <span className="truncate">{industry}</span>
                </div>
              </div>

            </CardContent>
          </Card>
        </Link>

        {/* Kebab menu in the top right corner */}
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="ghost"
              size="icon"
              className="absolute bottom-7 size-7 right-4 opacity-0 group-hover:opacity-100 transition-opacity z-10"
            >
              <MoreDotIcon className="size-4 text-foreground/70" />
              <span className="sr-only">
                {' '}
                {mainTranslate('menu')}
              </span>
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-55 p-0" align="end">
            <div className="py-1">
              <Link target="_blank" href={`/dashboard/projects/${id}`}>
                <Button
                  variant="ghost"
                  className="w-full justify-start rounded-none"
                >
                  <ArrowTopRightOnSquareIcon className="size-4" />
                  {' '}
                  {mainTranslate('open')}
                </Button>
              </Link>
              <Button
                variant="ghost"
                className="w-full justify-start rounded-none"
                onClick={e => handleShowInfoProject(e, project)}
              >
                <InformationCircleIcon className="size-4" />
                {mainTranslate('viewInfo')}

              </Button>
              {project.status !== ProjectStatusEnum.COMPLETED && (
                <Button
                  variant="ghost"
                  className="w-full justify-start text-green-500 rounded-none"
                  onClick={openConfirmCompleteProject}
                >
                  <CircleCheckIcon className="size-4" />
                  {mainTranslate('markCompleted')}

                </Button>
              )}
              <PermissionGuard permission={Permission.DELETE_PROJECT}>
                <Button
                  variant="ghost"
                  className="w-full justify-start text-destructive rounded-none"
                  onClick={openDeleteConfirm}
                >
                  <TrashIcon className="size-4" />
                  {mainTranslate('delete')}

                </Button>
              </PermissionGuard>
            </div>
          </PopoverContent>
        </Popover>
      </div>

      <ProjectFromModal
        isOpen={isOpen}
        closeModal={closeModal}
        project={projectSelected!}
      />

      <Modal isOpen={showConfirmModal} onClose={() => setShowConfirmModal(false)} className="max-w-md mx-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-foreground mb-2">
            {mainTranslate('noticeHeader')}
          </h3>
          <p className="text-sm text-muted-foreground my-6">
            {mainTranslate('descriptionHeader')}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={() => setShowConfirmModal(false)}
              disabled={isUpdating}
            >
              {t('cancel', { fallback: 'Cancel' })}
            </Button>

            <Button
              className="bg-blue-200 text-blue-600 hover:bg-blue-100"
              onClick={handleComplete}
              disabled={isUpdating}
            >
              {isUpdating
                ? (
                    <>{mainTranslate('completing')}</>
                  )
                : (
                    <>{mainTranslate('complete')}</>
                  )}
            </Button>

          </div>
        </div>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={showDeleteConfirm} onClose={closeDeleteConfirm} className="max-w-md mx-auto">
        <div className="p-6">
          <h3 className="text-lg font-medium text-foreground mb-2">
            {t('confirm_delete_project_title', { fallback: 'Delete Project' })}
          </h3>
          <p className="text-sm text-muted-foreground my-6">
            {t('confirm_delete_project')}
          </p>
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={closeDeleteConfirm}
              disabled={isDeleting}
            >
              {t('cancel', { fallback: 'Cancel' })}
            </Button>

            <Button
              variant="destructive"
              onClick={handleDelete}
              disabled={isDeleting}
            >
              {isDeleting
                ? (
                    <>{t('deleting', { fallback: 'Deleting...' })}</>
                  )
                : (
                    <>{t('delete', { fallback: 'Delete' })}</>
                  )}
            </Button>

          </div>
        </div>
      </Modal>
    </>
  );
}
