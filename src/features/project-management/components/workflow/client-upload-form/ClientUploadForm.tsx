'use client';

import Label from '@/shared/components/form/Label';
import SearchableSelect from '@/shared/components/form/SearchableSelect';
import FileUpload from '../initial-screening-form/FileUpload';
import React, { useEffect, useState } from 'react';
import type { IFileResponse, stateRouteAgent } from '@/shared/types/global';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useClientFileUploaded, useCurrentStep, useCurrentTask, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import { useCoAgent } from '@copilotkit/react-core';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { BriefAnalysisFlow } from '@/features/project-management/types/agent';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { ProjectCampaignEnum } from '@/features/project-management/types/project';
import type { fileUploadResponse, TemplateFiles } from '@/features/project-management/types/project';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { compareObjectArray } from '@/shared/utils/compareObject';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { useQueryClient } from '@tanstack/react-query';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import SelectModelAI from '../common/SelectModelAI';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useTranslations } from 'next-intl';

// Service options data - moved outside component to avoid re-renders
const SERVICE_OPTIONS = [
  { value: ProjectCampaignEnum.CORPORATE, label: 'Corporate' },
  { value: ProjectCampaignEnum.CRISIS_MANAGEMENT, label: 'Crisis Management' },
  { value: ProjectCampaignEnum.EVENT, label: 'Event' },
  { value: ProjectCampaignEnum.GR_ADVOCACY, label: 'GR-Advocacy' },
  { value: ProjectCampaignEnum.IMC, label: 'IMC' },
  { value: ProjectCampaignEnum.MARKET_RESEARCH, label: 'Market Research' },
  { value: ProjectCampaignEnum.MEDIA_RELATION_PR, label: 'Media Relation - PR' },
  { value: ProjectCampaignEnum.MI_BRAND_BRANDING, label: 'Mibrand Branding' },
  { value: ProjectCampaignEnum.PRODUCT_LAUNCH, label: 'Product Launch' },
  { value: ProjectCampaignEnum.SOCIAL_DIGITAL_CORPORATE, label: 'Social & Digital Corporate' },
  { value: ProjectCampaignEnum.SOCIAL_DIGITAL_PRODUCT, label: 'Social Media & Digital Product' },
  { value: ProjectCampaignEnum.TVC_VIDEO_PRODUCTION, label: 'TVC/Video Production' },
];

const ClientUploadForm: React.FC = () => {
  const t = useTranslations('workflow');

  const titleConfirm = t('common.titleConfirmChange');

  const titleUnSave = t('common.titleUnSave');

  const descriptionUnSave = t('common.descriptionUnSave');

  const descriptionConfirm = t('common.descriptionConfirm');
  //

  const [_files, setFiles] = useState<IFileResponse[]>([]);

  const [initialFile, setInitialFile] = useState<IFileResponse[]>([]);

  const [_templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);

  const [selectedOption, setSelectedOption] = useState<ProjectCampaignEnum>(ProjectCampaignEnum.IMC); // Default value

  const [selectedOptionInit, setSelectedOptionInit] = useState<ProjectCampaignEnum>(ProjectCampaignEnum.IMC); // Default value

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  // Custom Hook

  const currentStep = useCurrentStep();

  const currentTask = useCurrentTask();

  const currentStepId = currentStep?.id;

  const clientFileUploaded = useClientFileUploaded();

  const queryClient = useQueryClient();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const {
    getNextStepId,
    setClientFileUploaded,
    updateStatus,
    completeStep,
  } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data: fileUpload } = useGetInfoDetail<fileUploadResponse, any>(currentStep?.id ?? '');

  const { data: templates } = useGetListTemplates();

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  const { registerStep, clearStep } = useDirty();

  useEffect(() => {
    if (templates) {
      const templateSelect = templates.filter(template => template.campaign === selectedOption);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setTemplateFile(urlOptions);
    }
  }, [templates, selectedOption]);

  //  Set Agent

  const { setState: _setCoAgentsState } = useCoAgent<stateRouteAgent<BriefAnalysisFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  // const { appendMessage } = useCopilotChat();

  const saveInitialFile = (file: IFileResponse[]) => {
    const setInitialFileStore = () => {
      setInitialFile(file);
      setFiles(file);
    };

    setInitialFileStore();
  };

  const updateInitialFile = () => {
    const setFile = () => {
      const files = ((fileUpload?.stepInfo[0]?.infos[0]?.files ?? []).map(
        (file: any) => ({
          mimeType: file.type,
          originalname: file.name,
          key: file.file,
          filename: file.name,
          url: file.url,
          _id: file.id,
        }),
      ));

      const serviceOption = fileUpload?.stepInfo[0]?.infos[0]?.serviceOption ?? ProjectCampaignEnum.IMC;

      const model = fileUpload?.stepInfo[0]?.model;

      if (model) {
        setModelAIDefault(model);

        setModelAISelected(model);
      }

      saveInitialFile(files);
      setClientFileUploaded(files);
      setSelectedOption(serviceOption);

      setSelectedOptionInit(serviceOption);
    };

    setFile();
  };

  useEffect(() => {
    if (fileUpload && fileUpload.stepInfo.length) {
      // Check if there's a saved service option from API
      // Assuming the service option might be stored in a custom field or metadata
      const stepInfo = fileUpload.stepInfo[0];
      const savedServiceOption = (stepInfo as any)?.serviceOption || (stepInfo as any)?.metadata?.serviceOption;

      if (savedServiceOption && SERVICE_OPTIONS.some(option => option.value === savedServiceOption)) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setSelectedOption(savedServiceOption);
      }
    }

    if (!fileUpload || !fileUpload.stepInfo?.length) {
      return;
    }

    // Save initial uploaded file
    saveInitialFile(clientFileUploaded);

    // Update initial file state
    updateInitialFile();

    // Get the first stepInfo
    const stepInfo = fileUpload.stepInfo[0];

    // Extract saved service option (either direct or from metadata)
    const savedServiceOption
      = (stepInfo as any)?.serviceOption
        || (stepInfo as any)?.metadata?.serviceOption;

    // Set selected option if it's valid
    if (savedServiceOption && SERVICE_OPTIONS.some(option => option.value === savedServiceOption)) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setSelectedOption(savedServiceOption);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [fileUpload]);

  useEffect(() => {
    if (!_files.length || !currentStepId) {
      return;
    }
    const isChanged = !compareObjectArray(initialFile, _files);
    const isChangedSelectedOption = selectedOption !== selectedOptionInit;
    const isChangedModel = modelAIDefault !== modelAISelected;

    // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
    _setIsSaved(!(isChanged || isChangedSelectedOption || isChangedModel));

    registerStep(currentStepId, () => isChanged || isChangedSelectedOption || isChangedModel);

  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [_files, selectedOption, modelAISelected]);

  const handleFilesChange = React.useCallback((uploadedFiles: IFileResponse[]) => {
    setFiles(uploadedFiles);
  }, []);

  const handleOptionSelect = (value: string) => {
    setSelectedOption(+value);
  };

  // const handleSendMessage = () => {
  //   setCoAgentsState((prevState: any) => ({
  //     ...prevState,
  //     agent_name: AGENT_NAME_COPILOTKIT.ANSWER,
  //     [ENameStateAgentCopilotkit.ANALYSIS]: {
  //       ...prevState[ENameStateAgentCopilotkit.ANALYSIS],
  //       client_brief_url: getFile(_files),
  //       ...templateFile.reduce((result, template) => {
  //         if (template.type === ETypeFile.BRIEF_TEMPLATE) {
  //           result.template_brief_url = [
  //             ...(result.template_brief_url || []),
  //             ...getFile([template.file]),
  //           ];
  //         }
  //         if (template.type === ETypeFile.BRIEF_QUESTION) {
  //           result.question_brief_url = [
  //             ...(result.question_brief_url || []),
  //             ...getFile([template.file]),
  //           ];
  //         }
  //         return result;
  //       }, {} as any),
  //     },
  //   }));

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // };

  const getPayloadUpload = () => {
    return {
      stepInfos: [
        {
          order: 0,
          infos: [{
            files:
            _files.map(file => ({
              ...file,
              file: file.key,
              name: file.originalname,
              type: file.mimeType,
              id: file._id,
            })),
            serviceOption: selectedOption,
          }],
          model: modelAISelected,
        },
      ],
    };
  };

  const handleSaveData = async () => {
    const nextStepId = getNextStepId();

    if (!currentStepId) {
      return;
    }
    const payload = getPayloadUpload();

    await updateQuestionAnswer(
      payload,
      currentStepId,
    );

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', nextStepId], type: 'all' });

    updateStatus(nextStepId, EStatusTask.IN_PROGRESS);

    if (currentStep.status !== EStatusTask.COMPLETED) {
      await mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }
    if (currentTask && (currentTask.status !== EStatusTask.COMPLETED)) {
      await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    }

    setIsClickUnSaved(false);
    setTimeout(() => {
      completeStep(currentStepId);
    });
  };

  const resetData = () => {
    setFiles(initialFile);
    setSelectedOption(selectedOptionInit);
    setModelAISelected(modelAIDefault);
  };

  const handleFinishStep = () => {
    if (!currentStep) {
      return;
    }
    handleSaveData();
  };

  const handleConfirmPopUp = async () => {
    if (isClickUnSaved) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      resetData();
      setIsShowModal(false);
      return;
    }
    clearStep(currentStepId ?? '');

    const idNextStep = getNextStepId();

    const select = 'all';

    await mutateAsync({
      id: currentStepId ?? '',
      status: EStatusTask.PENDING,
      stepIds: [idNextStep],
      select,
      stepInfoIds: [],
      isGenerate: true,
    });

    await mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    _setIsSaved(true);
    handleFinishStep();
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const onSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    const isChanged = !compareObjectArray(initialFile, _files);
    const isChangedSelectedOption = selectedOption !== selectedOptionInit;
    const isChangedModel = modelAIDefault !== modelAISelected;

    if (((isChanged || isChangedSelectedOption || isChangedModel) && currentStep.status === EStatusTask.COMPLETED)) {
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStepId ?? '');
    if (currentStep.status !== EStatusTask.COMPLETED) {
      await handleSaveData();
    }
  };

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  return (
    <div className="relative">
      <div className="space-y-6 p-4 md:p-6">

        <SelectModelAI
          onChangeModel={handleChangeModelAI}
          defaultValue={modelAIDefault}
          disable={false}
        />

        {/* Service Options Dropdown */}
        <div className="mb-6">
          <Label htmlFor="campaign-type" className="mb-1.5 block text-primary">
            {t('clientUpload.campaignTitle')}
          </Label>
          <SearchableSelect
            options={SERVICE_OPTIONS}
            value={selectedOption}
            onChange={handleOptionSelect}
            placeholder={t('clientUpload.campaignTitle')}
            searchPlaceholder="Search..."
          />
        </div>

        <Label htmlFor="files" className="mb-1.5 block text-primary">
          {t('clientUpload.fileTitle')}
        </Label>
        <FileUpload initialFile={initialFile} onFilesChange={handleFilesChange} />
      </div>

      <WorkflowNavigation
        onComplete={onSubmit}
        nextButtonText={t('common.generate')}
        showPrevious={false}
      />

      {/* Modal for confirm content */}
      <GuardConfirmationModal
        open={isShowModal}
        onOpenChange={() => {}}
        title={titlePopup}
        description={descriptionPopUp}
        onConfirm={() => handleConfirmPopUp()}
        onCancel={() => handleCancelPopUp()}
        confirmText={t('common.continue')}
        cancelText={t('common.cancel')}
      />

      {/* Modal for guard */}
      <GuardConfirmationModal
        open={showDialog}
        onOpenChange={() => {}}
        title={title}
        description={message}
        onConfirm={onConfirm}
        onCancel={onCancel}
      />
    </div>
  );
};

export default ClientUploadForm;
