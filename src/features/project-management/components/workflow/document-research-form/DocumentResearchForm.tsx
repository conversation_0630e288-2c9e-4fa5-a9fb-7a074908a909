'use client';

import { ArrowUpTrayIcon, XMarkIcon } from '@heroicons/react/24/outline';
import type { CreateResearchPayload } from './validation/research.validation';
import Input from '@/shared/components/form/input/InputField';
import Label from '@/shared/components/form/Label';
import Select from '@/shared/components/form/Select';
import { Button } from '@/shared/components/ui/button';
import { Modal } from '@/shared/components/ui/modal';
import { useModal } from '@/shared/hooks/useModal';
import { zodResolver } from '@hookform/resolvers/zod';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { Controller, useForm } from 'react-hook-form';
import { createResearchSchema } from './validation/research.validation';
import { cn } from '@/shared/utils/utils';
import { Plus } from 'lucide-react';
import { useTranslations } from 'next-intl';
import { ReportType, reportTypeOptions } from '@/features/project-management/types/research';
import type { ResearchItem, ResearchTemplateCustomFile } from '@/features/project-management/types/research';
import { http } from '@/core/http/http';
import type { IFileResponse } from '@/shared/types/global';
import { toast } from 'sonner';
import { useResearchForDocumentReport } from '@/features/project-management/hooks/useResearchForDocumentReport';
import SearchableSelect from '@/shared/components/form/SearchableSelect';
import SearchableMultiSelect from '@/shared/components/form/SearchableMultiSelect';
import type { ItemFrameworkResponse } from '@/features/frameworks-templates/types';
import { useProjectResearchFramework } from '@/features/project-management/hooks/useProjectResearchFramework';
import { TEMPLATE_GENERATE_KEYS } from '@/features/project-management/constants/research';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { LoadingPage } from '@/shared/components/Loading/LoadingPage';

export type ResearchFormData = {
  name: string;
  reportInput: string[]; // Multiple research IDs from step 5
  reportType: ReportType; // Report type enum
  template: IFileResponse[];
  files: IFileResponse[];
  otherTemplate?: string;
  frameWorkId: string;
  description?: string;
  framework: ItemFrameworkResponse | null;
  templateId?: string;
  customFiles?: ResearchTemplateCustomFile[];
};

type ResearchFormProps = {
  onCreateResearch?: (data: ResearchFormData) => void;
  onUpdateResearch?: (research: ResearchFormData) => void;
  initialData?: ResearchItem;
  isEdit?: boolean;
  onClose?: () => void;
  prevId: string;
};

export function ResearchForm({
  onCreateResearch,
  onUpdateResearch,
  initialData,
  isEdit = false,
  onClose,
  prevId,
}: ResearchFormProps) {
  const { isOpen, openModal, closeModal } = useModal();
  const t = useTranslations('Project');

  const workflowTranslate = useTranslations('workflow');

  const [templateSearch, setTemplateSearch] = useState('');

  const [customTemplate, setCustomTemplate] = useState(
    isEdit && !initialData?.infos?.[0]?.frameWorkId ? initialData?.infos[0]?.otherTemplate : '',
  );

  const [customDescription, setCustomDescription] = useState('');

  const [uploadedFiles, setUploadedFiles] = useState<IFileResponse[]>(isEdit && initialData?.infos?.[0] && !initialData.infos[0].frameWorkId ? initialData?.infos[0]?.files ?? [] : []);

  const [isShowLoading, setIsShowLoading] = useState<boolean>(false);

  const abortControllerRef = useRef<AbortController | null>(null);

  // Fetch templates with search
  const { frameworkOptions: apiTemplateOptions, isLoading: isLoadingTemplates } = useProjectResearchFramework({
    searchQuery: templateSearch,
    limit: 50,
  });

  // Fetch research items from step 5 for report input
  const { researchOptions, isLoading: isLoadingResearch, handleSearch } = useResearchForDocumentReport(prevId);

  // Convert reportInput string array to Option format for defaultSelected
  const defaultSelectedOptions = useMemo(() => {
    if (!isEdit || !initialData?.infos?.[0]?.input) {
      return [];
    }

    const reportInputIds = initialData.infos[0].input;
    return researchOptions.filter(option =>
      reportInputIds.includes(option.value),
    ).map(option => ({
      ...option,
      selected: true,
    }));
  }, [isEdit, initialData?.infos, researchOptions]);

  // Find initial template based on frameWorkId
  const initialTemplateValue = useMemo(() => {
    if (!isEdit || !initialData?.infos?.[0]?.framework || !initialData?.infos?.[0]?.templateId) {
      return null;
    }

    for (const tem of initialData?.infos?.[0]?.framework.templates) {
      if (tem.id === initialData?.infos?.[0]?.templateId) {
        return tem;
      }
    }

    return 'other';
  }, [isEdit, initialData?.infos, apiTemplateOptions]);

  // Default form values - Remove apiTemplateOptions dependency to prevent infinite re-renders
  const defaultValues = useMemo(() => ({
    name: initialData?.name || '',
    reportInput: initialData?.infos?.[0]?.input || [],
    reportType: initialData?.infos?.[0]?.type ?? ReportType.Report,
    framework: initialData?.infos?.[0]?.framework ?? null,
    template: isEdit ? initialTemplateValue : null,
  }), [initialData, isEdit, initialTemplateValue]);

  // Initialize React Hook Form with Zod validation
  const {
    control,
    handleSubmit,
    reset,
    watch,
    setValue,
    formState: { errors, isSubmitting },
  } = useForm<CreateResearchPayload>({
    resolver: zodResolver(createResearchSchema),
    defaultValues,
  });

  // Watch template selection to show/hide file upload
  const selectedTemplate = watch('template');
  const selectedFramework = watch('framework');

  // Template options based on selected framework
  const templateOptions = useMemo(() => {
    if (selectedFramework === 'other') {
      return [{
        value: 'other',
        label: 'Other',
      }];
    }

    // If selectedFramework is a framework object from API
    if (selectedFramework && typeof selectedFramework === 'object') {
      const frameworkTemplates = selectedFramework.templates?.map((template: any) => ({
        value: template, // Template object with name and files
        label: template.name, // Display template name
      })) || [];

      return frameworkTemplates;
    }

    // If no framework selected, show only "Other"
    return [{
      value: 'other',
      label: 'Other',
    }];
  }, [selectedFramework]);

  // Reset template value when framework changes and auto-set to "other" when framework is "Other"
  useEffect(() => {
    if (selectedFramework === 'other') {
      setValue('template', 'other');
    } else if (selectedFramework && typeof selectedFramework === 'object' && selectedFramework.templates.every((t: any) => t?.id !== selectedTemplate?.id)) {
      setValue('template', null);
    }
  }, [selectedFramework, setValue]);

  const handleFileRemove = useCallback((fileToRemove: IFileResponse) => {
    setUploadedFiles(prev => prev.filter(file => file !== fileToRemove));
  }, []);

  const handleDragOver = useCallback((event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
  }, []);

  // Reset form and close modal
  const resetForm = useCallback(() => {
    reset(defaultValues);
    setUploadedFiles([]);
    setCustomTemplate(
      isEdit && !initialData?.infos?.[0]?.frameWorkId ? (initialData?.infos[0]?.otherTemplate || '') : '',
    );
    setUploadedFiles(
      isEdit && !initialData?.infos?.[0]?.frameWorkId ? (initialData?.infos[0]?.files ?? []) : [],
    );
    setCustomDescription('');
  }, [reset, defaultValues, isEdit, initialData]);

  // Close modal and reset form to default (empty) state
  const closeModalForm = useCallback(() => {
    closeModal();
    // Reset to empty form (not edit values)
    const emptyValues = {
      name: '',
      framework: null,
      template: null,
      reportType: ReportType.Report,
    };
    reset(emptyValues);
    setUploadedFiles([]);
    setCustomTemplate('');
    // Clear edit state in parent component
    if (onClose) {
      onClose();
    }
  }, [closeModal, reset, onClose]);

  // Open modal automatically when in edit mode
  useEffect(() => {
    if (isEdit && initialData) {
      openModal();
    }
  }, [isEdit, initialData, openModal]);

  // Initialize form when editing - use a simpler approach
  useEffect(() => {
    if (initialData && isEdit) {
      const formData = {
        name: initialData.name || '',
        reportInput: initialData.infos?.[0]?.input || [],
        reportType: initialData.infos?.[0]?.type ?? ReportType.Report,
        framework: initialData?.infos?.[0]?.framework ?? null,
        template: initialTemplateValue,
      };

      reset(formData);
      setCustomTemplate(
        isEdit && !initialData?.infos?.[0]?.frameWorkId ? initialData?.infos[0]?.otherTemplate : '',
      );
      setUploadedFiles(
        isEdit && !initialData?.infos?.[0]?.frameWorkId ? (initialData?.infos[0]?.files ?? []) : [],
      );
    }
  }, [initialData?.id, isEdit, reset]);

  const onSubmit = async (data: CreateResearchPayload) => {
    try {
      const { template, name, reportInput, reportType, framework } = data;
      const templateFiles: IFileResponse[] = [];
      const isOtherTemplate = template === 'other';

      if (!isOtherTemplate && template && typeof template === 'object') {
        // If template is a template object, get its files
        template.files?.forEach((f: any) => {
          templateFiles.push({ ...f.file, category: f.category });
        });
      }

      const customFiles: ResearchTemplateCustomFile[] = [];

      if (isOtherTemplate && !uploadedFiles.length) {
        setIsShowLoading(true);
        try {
          const baseUrl = window.location.origin;
          const abortController = new AbortController();
          abortControllerRef.current = abortController;

          const fetchPromises = TEMPLATE_GENERATE_KEYS.map((key) => {
            const data = {
              template_name: customTemplate,
              template_description: customDescription,
              example_template_url: [{ key }],
            };

            return fetch(`${baseUrl}/api/copilotkit-api`, {
              method: 'POST',
              body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.TEMPLATE_GENERATE }),
              signal: abortControllerRef.current?.signal,
            });
          });

          const responses = await Promise.all(fetchPromises);

          const [questionnaireRes, reportTemplateRes] = await Promise.all(responses.map(res => res.json()));

          if (questionnaireRes?.data?.result) {
            customFiles.push({ ...questionnaireRes?.data?.result as ResearchTemplateCustomFile, category: 'questionaire' });
          }

          if (reportTemplateRes?.data?.result) {
            customFiles.push({ ...reportTemplateRes?.data?.result as ResearchTemplateCustomFile, category: 'report' });
          }
        } catch (error) {
          console.log(error);
          setIsShowLoading(false);
        } finally {
          setIsShowLoading(false);
        }
      }

      const formData: ResearchFormData = {
        name,
        reportInput: reportInput || [],
        reportType: reportType || ReportType.Report,
        framework,
        template: templateFiles,
        files: uploadedFiles,
        otherTemplate: isOtherTemplate ? customTemplate : (template && typeof template === 'object' ? template.name : ''),
        frameWorkId: isOtherTemplate ? '' : (framework && typeof framework === 'object' ? framework.id : ''),
        templateId: isOtherTemplate ? '' : (template && typeof template === 'object' ? template.id : ''),
        description: isOtherTemplate ? '' : customDescription,
        customFiles,
      };

      if (isEdit && initialData && onUpdateResearch) {
        onUpdateResearch(formData);
      } else if (onCreateResearch) {
        onCreateResearch(formData);
      }

      resetForm();
      closeModalForm();
    } catch (error) {
      console.error('Failed to save research:', error);
    }
  };

  const getFileResponse = async (file: File) => {
    const formData = new FormData();
    formData.append('file', file);

    try {
      const uploadResponse = http.post<IFileResponse>({
        url: '/files/upload',
        data: formData,
        options: {
          headers: { 'Content-Type': 'multipart/form-data' },
        },
      });

      const res = await uploadResponse;
      return res;
    } catch (error) {
      console.log(error);
      throw error;
    }
  };

  const handleUploadFile = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || files.length === 0) {
      return;
    }

    try {
      toast.info(workflowTranslate('common.fileUploading'));

      // Handle multiple files
      const uploadPromises = Array.from(files).map(async (file) => {
        const { data } = await getFileResponse(file);
        return data;
      });

      const uploadedFileResponses = await Promise.all(uploadPromises);
      const validFiles = uploadedFileResponses.filter(file => file !== null);
      if (validFiles.length > 0) {
        setUploadedFiles(prev => [...prev, ...validFiles]);
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast.error(workflowTranslate('common.uploadFileFail'));
    }

    // Reset input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleFileDrop = useCallback(async (event: React.DragEvent<HTMLDivElement>) => {
    event.preventDefault();
    const files = event.dataTransfer.files;

    if (!files || files.length === 0) {
      return;
    }

    try {
      toast.info(workflowTranslate('common.fileUploading'));

      // Handle multiple files
      const uploadPromises = Array.from(files).map(async (file) => {
        const { data } = await getFileResponse(file);
        return data;
      });

      const uploadedFileResponses = await Promise.all(uploadPromises);
      const validFiles = uploadedFileResponses.filter(file => file !== null);

      if (validFiles.length > 0) {
        toast.success(workflowTranslate('common.uploadFileSuccess'));

        setUploadedFiles(prev => [...prev, ...validFiles]);
      }
    } catch (error) {
      console.error('File upload error:', error);
      toast.error(workflowTranslate('common.uploadFileFail'));
    }
  }, []);

  return (
    <>
      {isShowLoading && <LoadingPage />}

      <Button
        className={cn('gap-2 h-[200px] w-full border-2 border-dashed border-muted-foreground/25 hover:border-primary/50')}
        variant="outline"
        onClick={openModal}
      >
        <Plus className="w-5 h-5" />
        {isEdit ? t('edit_research', { fallback: 'Edit Research' }) : t('create_research', { fallback: 'Create Research' })}
      </Button>

      <Modal isOpen={isOpen} onClose={closeModalForm} className="max-w-[580px] m-4">
        <div className="no-scrollbar relative w-full max-w-[580px] rounded-3xl bg-white p-6 dark:bg-gray-900 overflow-hidden">
          <div className="px-2 pr-14">
            <h4 className="mb-2 text-2xl font-medium text-gray-800 dark:text-white/90">
              {isEdit ? t('edit_research', { fallback: 'Edit Research' }) : t('create_research', { fallback: 'Create Research' })}
            </h4>
            <p className="mb-6 text-sm text-gray-500 dark:text-gray-400 lg:mb-7">
              {t('research_form_description', { fallback: 'Please enter all the required information to proceed.' })}
            </p>
          </div>

          <form
            onSubmit={handleSubmit(onSubmit, (errors) => {
              console.log('Form validation failed:', errors);
            })}
            className="flex flex-col"
          >
            <div className="custom-scrollbar h-[350px] overflow-y-auto px-2 pb-3">
              <div className="grid grid-cols-1 gap-y-5">

                {/* Report Input */}
                <div>
                  <Controller
                    control={control}
                    name="reportInput"
                    render={({ field }) => (
                      <SearchableMultiSelect
                        label={t('report_input', { fallback: 'Report input' })}
                        options={researchOptions}
                        defaultSelected={defaultSelectedOptions}
                        onChange={field.onChange}
                        onSearch={handleSearch}
                        disabled={isLoadingResearch}
                        placeholder={t('placeholder.select_report_input', { fallback: 'Select multiple' })}
                        searchPlaceholder={t('placeholder.search_research', { fallback: 'Search research...' })}
                        isLoading={isLoadingResearch}
                      />
                    )}
                  />
                  {errors.reportInput && (
                    <p className="text-sm text-red-500 mt-1">{errors.reportInput.message}</p>
                  )}
                </div>

                {/* Research Name */}
                <div>
                  <Label htmlFor="name">
                    {t('report_name', { fallback: 'Research name' })}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="name"
                    render={({ field }) => (
                      <Input
                        id="name"
                        {...field}
                        placeholder={workflowTranslate('common.enter')}
                        type="text"
                        error={!!errors.name}
                        hint={errors.name?.message}
                      />
                    )}
                  />
                </div>

                {/* Report Type */}
                <div>
                  <Label htmlFor="reportType">
                    {t('report_type', { fallback: 'Report type' })}
                    {' '}
                    <span className="text-error-500">*</span>
                  </Label>
                  <Controller
                    control={control}
                    name="reportType"
                    render={({ field }) => (
                      <Select
                        key={`reportType-${field.value}-${isEdit ? initialData?.id : 'new'}`}
                        options={reportTypeOptions}
                        defaultValue={field.value}
                        onChange={value => field.onChange(+value)}
                        placeholder={t('select_report_type', { fallback: 'Select Report' })}
                        className="w-full text-black"
                      />
                    )}
                  />
                  {errors.reportType && (
                    <p className="text-sm text-red-500 mt-1">{errors.reportType.message}</p>
                  )}
                </div>

                {/* Research framework */}
                <div className="flex gap-3">
                  <div className="flex-1">
                    <Label htmlFor="framework">
                      {t('research_framework', { fallback: 'Research Framework' })}
                      {' '}
                      <span className="text-error-500">*</span>
                    </Label>
                    <Controller
                      control={control}
                      name="framework"
                      render={({ field }) => (
                        <SearchableSelect
                          options={apiTemplateOptions}
                          value={field.value || ''}
                          onChange={(value: string) => field.onChange(value)}
                          onSearchChange={setTemplateSearch}
                          searchValue={templateSearch}
                          placeholder={t('select_framework', { fallback: 'Select framework' })}
                          searchPlaceholder={t('search_frameworks', { fallback: 'Search frameworks...' })}
                          loading={isLoadingTemplates}
                          className="w-full"
                          positionView="fixed w-fit! h-full flex  flex-col"
                        />
                      )}
                    />
                    {errors.framework && (
                      <p className="text-sm text-red-500 mt-1">{workflowTranslate('businessForm.requiredFramework')}</p>
                    )}
                  </div>

                  {/* Research template */}
                  <div className="flex-1">
                    <Label htmlFor="template">
                      {t('research_template', { fallback: 'Research Template' })}
                      {' '}
                      <span className="text-error-500">*</span>
                    </Label>
                    <Controller
                      control={control}
                      name="template"
                      render={({ field }) => (
                        <SearchableSelect
                          options={templateOptions}
                          value={field.value || ''}
                          onChange={(value: string) => field.onChange(value)}
                          placeholder={t('select_template', { fallback: 'Select template' })}
                          className="w-full"
                          positionView="fixed w-fit! h-full flex  flex-col"
                        />
                      )}
                    />
                    {errors.template && (
                      <p className="text-sm text-red-500 mt-1">{workflowTranslate('businessForm.requiredTemplate')}</p>
                    )}

                  </div>
                </div>

                {/* Conditional fields when "Other" is selected */}
                {selectedTemplate === 'other' && (
                  <>
                    {/* Custom template description */}
                    <div>
                      <Label htmlFor="customTemplate" className="text-red-500">
                        {workflowTranslate('businessForm.typeNote')}
                      </Label>
                      <Input
                        id="customTemplate"
                        value={customTemplate}
                        onChange={(e) => {
                          return setCustomTemplate(e.target.value);
                        }}
                        placeholder={workflowTranslate('businessForm.typePlaceholder')}
                        type="text"
                      />
                    </div>

                    <div>
                      <Label htmlFor="customDescription" className="text-red-500">
                        {workflowTranslate('businessForm.descriptionNote')}
                      </Label>
                      <Input
                        id="customDescription"
                        value={customDescription}
                        onChange={e => setCustomDescription(e.target.value)}
                        placeholder={workflowTranslate('businessForm.descriptionPlaceholder')}
                        type="text"
                      />
                    </div>

                    {/* File upload section */}
                    <div>
                      <Label htmlFor="fileUpload">
                        {workflowTranslate('businessForm.templateFile')}
                      </Label>
                      {/* Drag and drop area */}
                      <div
                        className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center bg-gray-50 dark:bg-gray-800 dark:border-gray-600"
                        onDrop={e => handleFileDrop(e)}
                        onDragOver={handleDragOver}
                      >
                        <ArrowUpTrayIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                        <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                          {workflowTranslate('businessForm.dragAndDrop')}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                          {workflowTranslate('businessForm.files')}
                        </p>
                        <p className="text-xs text-gray-500 dark:text-gray-500 mb-4">
                          {workflowTranslate('businessForm.or')}
                        </p>
                        <Button
                          type="button"
                          variant="outline"
                          onClick={() => document.getElementById('fileInput')?.click()}
                          className="inline-flex items-center gap-2"
                        >
                          <ArrowUpTrayIcon className="h-4 w-4" />
                          {workflowTranslate('businessForm.browser')}
                        </Button>
                        <input
                          id="fileInput"
                          type="file"
                          onChange={e => handleUploadFile(e)}
                          multiple
                          className="hidden"
                        />
                      </div>

                      {/* Uploaded files display */}
                      {uploadedFiles.length > 0 && (
                        <div className="mt-4 space-y-2">
                          <div className="text-sm font-medium text-gray-700 dark:text-gray-300">
                            {workflowTranslate('businessForm.uploadFile')}
                            {' '}
                            (
                            {uploadedFiles.length}
                            )
                          </div>
                          {uploadedFiles.map((file, index) => (
                            <div
                              key={`${file.filename}-${index}`}
                              className="p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800"
                            >
                              <div className="flex items-center justify-between">
                                <div className="flex items-center gap-2">
                                  <div className="text-sm font-medium text-blue-900 dark:text-blue-100">
                                    {file.filename}
                                  </div>
                                </div>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  onClick={() => handleFileRemove(file)}
                                  className="h-6 w-6 p-0 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-200"
                                >
                                  <XMarkIcon className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                          ))}

                          {/* Clear all files button */}
                          {uploadedFiles.length > 1 && (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => setUploadedFiles([])}
                              className="text-red-600 hover:text-red-800 border-red-300 hover:border-red-400"
                            >
                              {workflowTranslate('businessForm.clearButton')}
                            </Button>
                          )}
                        </div>
                      )}
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="flex items-center gap-3 px-2 mt-6 lg:justify-end">
              <Button variant="outline" onClick={closeModalForm} disabled={isSubmitting}>
                {t('cancel', { fallback: 'Cancel' })}
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting
                  ? (isEdit ? t('updating', { fallback: 'Updating...' }) : t('creating', { fallback: 'Creating...' }))
                  : (isEdit ? t('update', { fallback: 'Update' }) : t('create', { fallback: 'Create' }))}
              </Button>
            </div>
          </form>
        </div>
      </Modal>
    </>
  );
}
