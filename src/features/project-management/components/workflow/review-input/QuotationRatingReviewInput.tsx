'use client';

import React, { useEffect, useMemo, useState } from 'react';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import WorkflowNavigation from '../layout/WorkflowNavigation';
import { useCurrentStep, useCurrentTask, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useCoAgent } from '@copilotkit/react-core';
import type { stateRouteAgent } from '@/shared/types/global';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import type { quotationOfWorkFlow } from '@/features/project-management/types/agent';
import type { ProjectCampaignEnum, TemplateFiles } from '@/features/project-management/types/project';
import { useGetListTemplates } from '@/features/project-management/hooks/useProjectTemplate';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import { useQueryClient } from '@tanstack/react-query';
import SelectModelAI from '../common/SelectModelAI';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { useTranslations } from 'next-intl';

const QuotationRatingReviewInput: React.FC = () => {
  const t = useTranslations('workflow');

  const [markdown, setMarkdown] = useState<string>('');
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [_templateFile, setTemplateFile] = useState<TemplateFiles[]>([]);
  const [campaignSelected, setCampaignSelected] = useState<ProjectCampaignEnum | null>(null);
  const [isViewBrief, setIsViewBrief] = useState<boolean>(false);

  const currentStep = useCurrentStep();
  const currentStepId = currentStep?.id;
  const workflow = useWorkflowTasks();

  // FIXME: update later
  const idSecondStep = workflow[1]?.steps[0]?.id;

  const currentTask = useCurrentTask();

  const {
    completeStep,
    updateStatus,
    getNextStepId,
  } = useWorkflowActions();

  const { mutateAsync } = useUpdateStatusStep();

  const { data } = useGetInfoDetail<any, any>(currentStepId ?? '');

  const { data: clientUploadData } = useGetInfoDetail<any, any>(idSecondStep ?? '');

  const { data: templates } = useGetListTemplates();

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const [isShowModal, setIsShowModal] = useState(false);

  const titleConfirm = t('common.titleConfirmChange');

  const descriptionConfirm = t('common.descriptionConfirm');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const queryClient = useQueryClient();

  useEffect(() => {
    if (clientUploadData?.stepInfo.length && clientUploadData?.stepInfo[0]?.infos?.length) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setCampaignSelected(clientUploadData?.stepInfo[0]?.infos[0]?.serviceOption);
    }
  }, [clientUploadData]);

  useEffect(() => {
    if (templates && campaignSelected) {
      const templateSelect = templates.filter(template => template.campaign === campaignSelected);
      let urlOptions: TemplateFiles[] = [];
      templateSelect.forEach(template => urlOptions = [...urlOptions, ...template.files]);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setTemplateFile(urlOptions);
    }
  }, [templates, campaignSelected]);

  const updateMarkdownToState = (data: string) => {
    const updateData = () => {
      setMarkdown(data);
      setIsLoading(false);
    };

    updateData();
    return true;
  };

  // AI agent hooks
  // const { appendMessage } = useCopilotChat();
  const { setState: _setCoAgentsState } = useCoAgent<stateRouteAgent<quotationOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
    initialState: {
    },
  });

  // const handleSendPrompt = React.useCallback((markdown: string) => {
  //   setCoAgentsState((prevState: any) => ({
  //     ...prevState,
  //     agent_name: AGENT_NAME_COPILOTKIT.QUOTATION,

  //     [ENameStateAgentCopilotkit.QUOTATION]: {
  //       ...prevState[ENameStateAgentCopilotkit.QUOTATION],
  //       sow_analysis: markdown,
  //       ...templateFile.reduce((result, template) => {
  //         if (template.type === ETypeFile.QUOTATION) {
  //           result.quotation_template_url = [
  //             ...(result.quotation_template_url || []),
  //             ...getFile([template.file]),
  //           ];
  //         }

  //         return result;
  //       }, {} as any),
  //     },
  //   }));

  //   appendMessage(
  //     new TextMessage({
  //       content: MESSAGE_SEND_ROUTE_AGENT,
  //       role: Role.Developer,
  //     }),
  //   );
  // }, [setCoAgentsState, appendMessage, templateFile]);

  useEffect(() => {
    if (data?.stepInfoPrevious.length && data?.stepInfoPrevious[0]?.infos?.length && data?.stepInfoPrevious[0]?.infos[0]?.value) {
      updateMarkdownToState(data?.stepInfoPrevious[0]?.infos[0]?.value);
    }

    if (data?.stepInfo.length && data?.stepInfo[0]?.infos.length) {
      const model = data?.stepInfo[0].model ?? EValueModelAI.GPT;
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);
    }

    if (data?.stepInfoNext.length && data?.stepInfoNext[0]?.infos?.length && data?.stepInfoNext[0]?.infos[0]?.value) {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsViewBrief(true);
    } else {
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsViewBrief(false);
    }
  }, [data]);

  const isChangedModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAIDefault, modelAISelected]);

  const saveDataInDB = async () => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markdown }],
          model: modelAISelected,
        },
      ],
    };

    await updateQuestionAnswer(payload, currentStepId);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    if (isChangedModel && currentStep.status === EStatusTask.COMPLETED) {
      setIsShowModal(true);
      return;
    }

    // handleSendPrompt(markdown);
    if (currentStep.status !== EStatusTask.COMPLETED) {
      await saveDataInDB();
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.IN_PROGRESS });

      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
    }

    completeStep(currentStepId);
  };

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleConfirmPopUp = async () => {
    if (!currentStepId) {
      return;
    }
    const nextStepId = getNextStepId();
    await mutateAsync({
      id: currentStepId,
      status: EStatusTask.IN_PROGRESS,
      select: 'all',
      isGenerate: true,
      stepIds: [nextStepId],
      stepInfoIds: [],
      stepInfoIdsGenerate: [],
    });
    await saveDataInDB();

    await queryClient.invalidateQueries({ queryKey: ['getInfoDetail', nextStepId], type: 'all' });

    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);
    updateStatus(nextStepId, EStatusTask.IN_PROGRESS);
    setIsShowModal(false);

    completeStep(currentStepId);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">{t('common.loading')}</div>
          <ProjectCardSkeleton />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <SelectModelAI
            onChangeModel={handleChangeModelAI}
            defaultValue={modelAIDefault}
            disable={false}
            top="top-60"
            right="right-14"
          />

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6">

            <WorkflowNavigation
              onComplete={handleSubmit}
              nextButtonText={isViewBrief ? t('common.nextStep') : t('common.generate')}
              showPrevious={true}
              prevButtonText={t('common.back')}
              panelClass=""
            />
          </div>

          <div className="">

            <MarkdownRenderer content={markdown} />
          </div>

          {/* Modal for confirm content */}
          <GuardConfirmationModal
            open={isShowModal}
            onOpenChange={() => {}}
            title={titleConfirm}
            description={descriptionConfirm}
            onConfirm={() => handleConfirmPopUp()}
            onCancel={() => handleCancelPopUp()}
            confirmText={t('common.continue')}
            cancelText={t('common.cancel')}
          />
        </div>
      );
};

export default QuotationRatingReviewInput;
