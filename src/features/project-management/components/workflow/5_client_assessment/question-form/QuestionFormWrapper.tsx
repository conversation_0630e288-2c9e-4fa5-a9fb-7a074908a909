import { useEffect, useMemo, useRef, useState } from 'react';
import HeaderResearch from '../HeaderResearch';
import { EQuantitative } from '@/features/project-management/types/questionnaire';
import type { OptionChangeViewType } from '@/features/project-management/types/questionnaire';
import QuantityQuestionnaire from '../../questionnaire/QuanityQuestionnaire';
import type { QuestionnaireFormTypeRef } from '@/features/questionnaire/components/layouts/QuestionnaireForm';
import type { QuestionnaireResponse } from '@/features/questionnaire/types/questionnaire';
import { EEndpointApiCopilotkit } from '@/shared/enums/global';
import { updateNameFieldForQuestionnaire } from '@/features/project-management/utils/workflowUtils';
import { useParams } from 'next/navigation';
import { useCurrentStep, useProjectName, useWorkflowActions, useWorkflowTasks } from '@/features/project-management/stores/project-workflow-store';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { documentFileUpload, ScoringReportDataType } from '@/features/project-management/types/project';
import ProjectCardSkeleton from '../../../project-list/ProjectCardSkeleton';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import ChartSurveyWrapper from '../../discovery-questionnaire/quantitative-questionnaire/chart-survey/ChartSurveyWrapper';
import AnalysisReportQuantitative from './AnalysisReportQuantitative';
import type { AnalysisReportQuantitativeRef } from './AnalysisReportQuantitative';
import type { IFileResponse } from '@/shared/types/global';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { useQueryClient } from '@tanstack/react-query';
import { markdownToHTMLToDocFile } from '@/shared/components/ui/editor/parser';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import MessageWarning from '../../common/MessageWarning';
import { useTranslations } from 'next-intl';

type QuestionViewType = 'questionnaire' | 'summary' | 'analysis';

type QuestionFormType = {
  data: any[];
  stepId: string;
  id: string;
  templates: IFileResponse[];
  evaluationFramework: string;
  nameForm: string;
  onBackDashboard: () => void;
  onOpenDetailScore: (data: string) => void;
};

const QuestionFormWrapper: React.FC<QuestionFormType> = ({
  data,
  stepId,
  id,
  templates,
  evaluationFramework,
  nameForm,
  onOpenDetailScore,
  onBackDashboard,
}) => {
  const t = useTranslations('workflow');

  const toggleOptions = [
    { id: 'questionnaire' as QuestionViewType, label: 'Questionnaire' },

  ];

  const extendOptions = [
    // { id: 'summary' as QuestionViewType, label: 'Answer Summary' },
    { id: 'analysis' as QuestionViewType, label: 'Analysis' },
  ];

  const [options, setOptions] = useState<OptionChangeViewType[]>(() => toggleOptions);

  const [selectedType, setSelectedType] = useState<QuestionViewType>('questionnaire');

  const [quantity, setQuantity] = useState<QuestionnaireResponse | null>(null);

  const [analysisBrief, setAnalysisBrief] = useState<string>('');

  const [dataAnalysis, setDataAnalysis] = useState<string>('');

  const [fileAnalysis, setFileAnalysis] = useState<IFileResponse[]>([]);

  const [isAnalysis, setIsAnalysis] = useState<boolean>(false);

  const [isFinish, _setIsFinish] = useState<boolean>(false);

  const [isLoading, setIsLoading] = useState(true);

  const [isLoadingSummary, setIsLoadingSummary] = useState(true);

  const [isLoadingAnalysis, setIsLoadingAnalysis] = useState(false);

  const [isFirstCall, setIsFirstCall] = useState<boolean>(false);

  const [scoringData, setScoringData] = useState<ScoringReportDataType | null>(null);

  const [isReGenData, setIsReGenData] = useState<boolean>(false);

  const [textCopyQuestionnaire, setTextCopyQuestionnaire] = useState<string>('');

  const [idQuestionnaire, setIdQuestionnaire] = useState<string>('');

  const [idAnalysis, setIdAnalysis] = useState<string>('');

  const [idScoring, setIdScoring] = useState<string>('');

  const [modelAIQuesDefault, setModelAIQuesDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAIQuesSelected, setModelAIQuesSelected] = useState<string>(EValueModelAI.GPT);

  const [modelAIAnalysisDefault, setModelAIAnalysisDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAIAnalysisSelected, setModelAIAnalysisSelected] = useState<string>(EValueModelAI.GPT);

  const form = useRef<QuestionnaireFormTypeRef>(null);

  const analysisRef = useRef<AnalysisReportQuantitativeRef>(null);

  const params = useParams<{ id: string }>();

  const workflow = useWorkflowTasks();

  const currentStep = useCurrentStep();

  const projectName = useProjectName();

  const idSOW = workflow[2]?.steps[1]?.id;

  const { data: dataSOW } = useGetInfoDetail<any, documentFileUpload>(idSOW ?? '');

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { mutateAsync: refreshData } = useUpdateStatusStep();

  const queryClient = useQueryClient();

  const abortControllerRef = useRef<AbortController | null>(null);

  const {
    completeStep,
  } = useWorkflowActions();

  const handleSelectType = (option: OptionChangeViewType) => {
    if (!quantity) {
      return;
    }
    setSelectedType(option.id as QuestionViewType);
  };

  useEffect(() => {
    if (dataSOW && dataSOW.stepInfo.length) {
      const brief = dataSOW?.stepInfo[0]?.infos[0]?.value ?? '';

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setAnalysisBrief(brief);
    }
  }, [dataSOW]);

  const saveDataFromAI = async (form: QuestionnaireResponse) => {
    const payload = {
      formStepId: id,
      stepInfos: [
        {
          order: 3,
          type: EQuantitative.QUESTIONNAIRE,
          infos: [
            { form },
          ],
          model: modelAIQuesSelected,
        },

      ],
    };

    await updateQuestionAnswer(
      payload,
      stepId,
    );
  };

  const getDataQuestionnaire = async () => {
    if (isFirstCall) {
      return;
    }

    if ((!analysisBrief)) {
      return;
    }
    setIsFirstCall(true);
    const payload = {
      project_id: params.id,
      brief_analysis: analysisBrief,
      llm: modelAIQuesSelected,
      questionnaire_template_url: [{
        key: 'https://minastik-store.s3.ap-northeast-1.amazonaws.com/templates/Questionaire+Template.docx',
      }],
    };

    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data: payload, endpoint: EEndpointApiCopilotkit.QUANTITY }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();

      const quantity = res.data.result?.data;

      setTextCopyQuestionnaire(`${baseUrl}/questionnaire?id=${stepId}`);

      setIsLoading(false);
      if (quantity) {
        setQuantity(updateNameFieldForQuestionnaire(quantity));
        setIdQuestionnaire(quantity.id ?? '');
        setOptions([...toggleOptions, ...extendOptions]);
      }

      if (!quantity) {
        return;
      }
      saveDataFromAI(quantity);
    } catch (error) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (!data.length) {
      getDataQuestionnaire();
    } else {
      const dataQuestionnaire = data.find(d => d.type === EQuantitative.QUESTIONNAIRE);
      if (dataQuestionnaire) {
        const data = dataQuestionnaire.infos[0].form;

        const model = dataQuestionnaire.model;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIQuesDefault(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIQuesSelected(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setOptions([...toggleOptions, ...extendOptions]);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdQuestionnaire(data.id ?? '');
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setQuantity(updateNameFieldForQuestionnaire(data));
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsLoading(false);
      } else {
        getDataQuestionnaire();
      }
      const dataAnalysis = data.find(d => d.type === EQuantitative.ANALYSIS);
      if (dataAnalysis) {
        const data = dataAnalysis.infos[0].form;
        // const status = dataAnalysis.infos[0]?.status;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdAnalysis(dataAnalysis.id);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setDataAnalysis(data);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsAnalysis(true);

        const baseUrl = window.location.origin;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setTextCopyQuestionnaire(`${baseUrl}/questionnaire?id=${stepId}`);

        // if (status) {
        // // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        //   setIsFinish(status === EStatusTask.COMPLETED);
        // }
      }

      const fileAnalysis = data.find(d => d.type === EQuantitative.FILES);
      if (fileAnalysis) {
        const data = fileAnalysis.infos[0].files;

        const model = fileAnalysis.model;

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIAnalysisSelected(model);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setModelAIAnalysisDefault(model);
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setFileAnalysis(data ?? [] as IFileResponse[]);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setOptions([...toggleOptions, ...extendOptions]);
      }

      const scoringData = data.find(d => d.type === EQuantitative.SCORING);

      if (scoringData) {
        const data = scoringData.infos[0].value;
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIdScoring(scoringData.id);

        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setScoringData(data);
      }

      const isGenInform = data.some(d => d.isGenerate && d.order !== 0);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsReGenData(isGenInform);
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [data, analysisBrief]);

  const onSubmit = () => {};

  const onComplete = () => {
    if (dataAnalysis && quantity) {
      completeStep(stepId);
    }
  };

  const handleReGen = async () => {
    if (selectedType === 'questionnaire') {
      const idResetData = data.filter(d => d.order === 3).map(d => d.id);
      const idChangeReGen = data.filter(d => d.order !== 3).map(d => d.id);
      setIsLoading(true);
      setQuantity(null);
      setIsFirstCall(false);
      await refreshData({
        id: stepId,
        status: EStatusTask.COMPLETED,
        stepIds: [],
        stepInfoIds: idResetData,
        stepInfoIdsGenerate: idChangeReGen,
        select: 'all',
        isGenerate: true,
      });

      queryClient.invalidateQueries({ queryKey: ['getInfoDetail', stepId], type: 'all' });

      return;
    }
    const idsInForm = data.filter(d => d.order !== 3 && d.order !== 4).map(d => d.id);
    setIsLoadingAnalysis(true);
    setIsReGenData(false);
    await refreshData({
      id: stepId,
      status: EStatusTask.COMPLETED,
      stepIds: [],
      stepInfoIds: idsInForm,
      select: 'all',
      isGenerate: true,
    });

    if (analysisRef.current) {
      analysisRef.current.getAnalysis();
    }
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(dataAnalysis);

    await downloadMDToFile(html, projectName, nameStep, nameForm, selectedType);
  };

  const isChangedModelQuestionnaire = useMemo(() => {
    return modelAIQuesDefault !== modelAIQuesSelected;
  }, [modelAIQuesDefault, modelAIQuesSelected]);

  const isChangedModelAnalysis = useMemo(() => {
    return modelAIAnalysisDefault !== modelAIAnalysisSelected;
  }, [modelAIAnalysisDefault, modelAIAnalysisSelected]);

  const handleChangeModel = (model: string) => {
    if (selectedType === 'questionnaire') {
      setModelAIQuesSelected(model);
    } else if (selectedType === 'analysis') {
      setModelAIAnalysisSelected(model);
    }
  };

  return (
    <>
      <div className="sticky top-[70px] bg-white z-100 pb-2">
        <HeaderResearch
          textCopy={textCopyQuestionnaire}
          // isViewCopyBox={selectedType === 'questionnaire' && !!quantity}
          isViewCopyBox={false}
          header={t('deskResearch.discoveryHeader')}
          description={t('deskResearch.descriptionHeader')}
          options={options}
          selectedType={selectedType}
          isHiddenBackButton={!(selectedType === 'analysis') || (selectedType === 'analysis' && !isAnalysis)}
          handleSelectedType={handleSelectType}
          onClickApprove={onSubmit}
          onSaveAndNextStep={onComplete}
          onBackDashboard={onBackDashboard}
          onBackUploadFile={() => setIsAnalysis(false)}
          onOpenDetailScore={onOpenDetailScore}
          isScoringAI={selectedType === 'analysis' && !!scoringData}
          dataScoring={scoringData}
          isShowButtonReGen={
            (selectedType === 'questionnaire'
              && isChangedModelQuestionnaire
            )
            || (selectedType === 'analysis'
              && (isChangedModelAnalysis || isReGenData) && isAnalysis)

          }
          onReGen={handleReGen}
          onDownloadFile={handleDownloadFile}
          isShowModel={(
            selectedType === 'questionnaire' && !isLoading)
          || (selectedType === 'analysis' && !isLoadingAnalysis)}
          onChangeModel={handleChangeModel}
          modelAIDefault={
            selectedType === 'questionnaire'
              ? modelAIQuesDefault
              : modelAIAnalysisDefault
          }
          isShowDownloadIcon={
            selectedType === 'analysis' && !!dataAnalysis && !isLoadingAnalysis
          }
        />
      </div>

      { (selectedType === 'questionnaire'
        ? (
            isLoading
              ? (
                  <div className="mt-4">
                    <ProjectCardSkeleton />
                    <MessageWarning />
                  </div>
                )
              : (
                  <div className="mt-4">
                    <QuantityQuestionnaire ref={form} questionnaire={quantity} />
                  </div>
                )
          )
        : selectedType === 'summary'
          ? (
              isLoadingSummary
                ? (
                    <div className="mt-4">
                      <ProjectCardSkeleton />
                      <MessageWarning />
                    </div>
                  )
                : (
                    <div className="mt-4">
                      <ChartSurveyWrapper setIsLoading={setIsLoadingSummary} idQuestionnaire={idQuestionnaire} />
                    </div>
                  )
            )
          : (

              <div className="mt-4">
                <AnalysisReportQuantitative
                  ref={analysisRef}
                  idQuestionnaire={idQuestionnaire}
                  fileAnalysis={fileAnalysis}
                  dataAnalysis={dataAnalysis}
                  id={id}
                  stepId={stepId}
                  templates={templates}
                  evaluationFramework={evaluationFramework}
                  isFinish={isFinish}
                  isAnalysis={isAnalysis}
                  isLoading={isLoadingAnalysis}
                  setIsAnalysis={setIsAnalysis}
                  setScoringData={setScoringData}
                  setIsLoading={setIsLoadingAnalysis}
                  idScoring={idScoring}
                  idAnalysis={idAnalysis}
                  modelDefault={modelAIAnalysisDefault}
                  modelSelected={modelAIAnalysisSelected}
                />
              </div>

            ))}
    </>
  );
};

export default QuestionFormWrapper;
