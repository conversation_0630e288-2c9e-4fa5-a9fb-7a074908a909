'use client';

import React from 'react';
import { toast } from 'sonner';
import { Button } from '@/shared/components/ui/button';
import {
  useCurrentStep,
  useCurrentTask,
  useWorkflowActions,
} from '../../../stores/project-workflow-store';
import { useTranslations } from 'next-intl';

type WorkflowNavigationProps = {
  onComplete?: () => void;
  disableNext?: boolean;
  nextButtonText?: string;
  showPrevious?: boolean;
  children?: React.ReactNode;
  prevButtonText?: string;
  panelClass?: string;
};

const WorkflowNavigation: React.FC<WorkflowNavigationProps> = ({
  onComplete,
  disableNext = false,
  nextButtonText = 'Next Step',
  prevButtonText = 'Previous Step',
  panelClass = 'bg-background border-t',
  showPrevious = true,
  children,
}) => {
  const t = useTranslations('workflow');

  const currentTask = useCurrentTask();
  const currentStep = useCurrentStep();

  const { moveToNextStep, moveToPreviousStep, completeStep } = useWorkflowActions();

  const handleNext = () => {
    if (!currentStep && !currentTask) {
      return;
    }

    // If there's a custom completion handler, use it
    if (onComplete) {
      onComplete();
      return;
    }

    // For tasks without steps (task is the step itself)
    if (currentTask && currentTask.steps.length === 0) {
      completeStep(currentTask.id);

      toast.success(t('common.taskCompleted'), {
        duration: 3000,
      });
      return;
    }

    // For normal steps within tasks
    if (currentStep) {
      // Use moveToNextStep which will handle completing the step and navigating
      moveToNextStep();

      toast.success(t('common.stepCompleted'), {
        duration: 3000,
      });
    }
  };

  return (
    <div className={`sticky bottom-0 left-0 right-0  border-border p-4 z-20 flex justify-center gap-4 ${panelClass}`}>

      {showPrevious && (
        <Button
          type="button"
          variant="outline"
          onClick={moveToPreviousStep}
        >
          {prevButtonText}
        </Button>
      )}

      <Button
        type="button"
        onClick={handleNext}
        disabled={disableNext || (!currentStep && (!currentTask || currentTask.steps.length > 0))}
      >
        {nextButtonText}
      </Button>
      {children}
    </div>
  );
};

export default WorkflowNavigation;
