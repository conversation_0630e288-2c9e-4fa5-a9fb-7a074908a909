'use client';

import { <PERSON><PERSON> } from '@/shared/components/ui/button';
import { ArrowDownTrayIcon, CheckBadgeIcon, FileEditIcon } from '@/shared/icons';
import { useEffect, useMemo, useRef, useState } from 'react';
import { useCoAgent } from '@copilotkit/react-core';
import type { scopeOfWorkFlow } from '@/features/project-management/types/agent';
import ProjectCardSkeleton from '../../project-list/ProjectCardSkeleton';
import { MarkdownRenderer } from '@/shared/components/ui/markdown/MarkdownRenderer';
import Editor from '@/shared/components/ui/editor/editor';
import type { EditorContentChanged, stateRouteAgent } from '@/shared/types/global';
import { useCurrentStep, useCurrentTask, useProjectName, useWorkflowActions } from '@/features/project-management/stores/project-workflow-store';
import type { StepInfosPayload } from '@/features/project-management/types/evaluation';
import { useProjectUpdateQuestionAnswer } from '@/features/project-management/hooks/useProjectUpdateQuestionAnswer';
import { AGENT_ROUTE_NAME } from '@/shared/constants/global';
import { EEndpointApiCopilotkit, ENameStateAgentCopilotkit } from '@/shared/enums/global';
import { useUpdateStatusStep } from '@/features/project-management/hooks/useUpdateStatusStep';
import { EStatusTask } from '@/features/project-management/types/workflow';
import { useGetInfoDetail } from '@/features/project-management/hooks';
import type { stepInfosMarkdownResponse } from '@/features/project-management/types/project';
import { useParams } from 'next/navigation';
import { useRouteGuardWithDialog } from '@/shared/hooks/route-guard/use-route-guard-with-dialog';
import { GuardConfirmationModal } from '@/shared/components/modals/GuardConfirmationModal';
import { htmlToMarkdownVer2, markdownToHTMLToDocFile, markdownToHtmlVer2 } from '@/shared/components/ui/editor/parser';
import { useDirty } from '@/features/project-management/contexts/DirtyStepContext';
import { downloadMDToFile } from '@/shared/utils/convertMDtoDocFile';
import { EValueModelAI } from '@/features/project-management/constants/modelAI';
import SelectModelAI from '../common/SelectModelAI';
import MessageWarning from '../common/MessageWarning';
import { useTranslations } from 'next-intl';

const ProjectScopingGeneration: React.FC = () => {
  const t = useTranslations('workflow');

  const [isShowEditButton, _setIsShowEditButton] = useState(true);

  const [isEditMode, setIsEditMode] = useState<boolean>(false);

  const [markdown, setMarkdown] = useState<string>('');

  const [form, setForm] = useState<string>('');

  const [isLoading, setIsLoading] = useState<boolean>(true);

  const [isSaved, _setIsSaved] = useState(true);

  const [isClickUnSaved, setIsClickUnSaved] = useState(false);

  const [isShowModal, setIsShowModal] = useState(false);

  const [isShowButtonReGen, setIsShowButtonReGen] = useState<boolean>(false);

  const titleConfirm = t('common.titleConfirmChange');

  const titleUnSave = t('common.titleUnSave');

  const descriptionUnSave = t('common.descriptionUnSave');

  const descriptionConfirm = t('common.descriptionConfirm');

  const [titlePopup, setTitlePopUp] = useState<string>(titleConfirm);

  const [descriptionPopUp, setDescriptionPopUp] = useState<string>(descriptionConfirm);

  const [modelAIDefault, setModelAIDefault] = useState<string>(EValueModelAI.GPT);

  const [modelAISelected, setModelAISelected] = useState<string>(EValueModelAI.GPT);

  const currentStep = useCurrentStep();

  const currentStepId = currentStep?.id;

  const currentTask = useCurrentTask();

  const projectName = useProjectName();

  const { mutateAsync } = useUpdateStatusStep();

  const { registerStep, clearStep } = useDirty();

  const { completeStep, updateStatus } = useWorkflowActions();

  const { updateQuestionAnswer } = useProjectUpdateQuestionAnswer();

  const { state } = useCoAgent<stateRouteAgent<scopeOfWorkFlow>>({
    name: AGENT_ROUTE_NAME,
  });

  const params = useParams<{ id: string }>();

  const { data: sowAnalysis } = useGetInfoDetail<stepInfosMarkdownResponse, stepInfosMarkdownResponse>(currentStepId ?? '');

  const abortControllerRef = useRef<AbortController | null>(null);

  const { showDialog, title, message, onConfirm, onCancel } = useRouteGuardWithDialog({
    when: !isSaved,
    title: t('common.titleUnSave'),
    message: t('common.descriptionGuard'),
  });

  const updateMarkdownToState = (data: string) => {
    const updateState = () => {
      setMarkdown(data);
      setForm(data);
      setIsLoading(false);
    };
    updateState();
  };

  const saveDataFromAI = async (markdown: string) => {
    // convert same format data
    const html = await markdownToHtmlVer2(markdown);
    const markDownConvert = htmlToMarkdownVer2(html);

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: markDownConvert }],
          model: modelAISelected,
        },
      ],
    };

    if (currentStepId) {
      await updateQuestionAnswer(payload, currentStepId);
      mutateAsync({ id: currentStepId, status: EStatusTask.IN_PROGRESS });
    }
  };

  const getGenerateData = async (data: any) => {
    try {
      const baseUrl = window.location.origin;
      const abortController = new AbortController();
      abortControllerRef.current = abortController;

      const response = await fetch(`${baseUrl}/api/copilotkit-api`, {
        method: 'POST',
        body: JSON.stringify({ data, endpoint: EEndpointApiCopilotkit.SCOPE }),
        signal: abortControllerRef.current.signal,
      });

      const res = await response.json();
      const brief = res.data.result;

      updateMarkdownToState(brief);
      saveDataFromAI(brief);
    } catch (error: any) {
      console.log(error);
    }
  };

  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, []);

  useEffect(() => {
    if (sowAnalysis && sowAnalysis?.stepInfo.length) {
      const markdown = sowAnalysis.stepInfo[0]?.infos[0]?.value;
      const isReGen = sowAnalysis.stepInfo[0]?.isGenerate ?? false;

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setIsShowButtonReGen(isReGen);

      updateMarkdownToState(markdown ?? '');
      // if (currentStep?.status === EStatusTask.COMPLETED) {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(false);
      // } else {
      //   // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      //   setIsShowEditButton(true);
      // }
    } else {
      if (sowAnalysis && sowAnalysis?.stepInfoPrevious.length) {
        // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
        setIsShowButtonReGen(false);
        const briefFile = sowAnalysis?.stepInfoPrevious?.[0]?.infos?.[0]?.value ?? '';
        const data = {
          project_id: params.id,
          brief_analysis: briefFile,
          llm: modelAISelected,
        };
        getGenerateData(data);
      }
    }

    if (sowAnalysis && sowAnalysis?.stepInfoPrevious.length) {
      const model = sowAnalysis.stepInfoPrevious[0]?.model ?? EValueModelAI.GPT;

      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAIDefault(model);
      // eslint-disable-next-line react-hooks-extra/no-direct-set-state-in-use-effect
      setModelAISelected(model);
    }
  }, [sowAnalysis]);

  useEffect(() => {
    const scopeState = state[ENameStateAgentCopilotkit.SCOPE];
    if (scopeState?.sow_analysis_output && scopeState.sow_analysis_process && scopeState.sow_analysis_process === 'done') {
      updateMarkdownToState(scopeState?.sow_analysis_output);
    }
  }, [state]);

  const compareMarkdown = (form?: string) => {
    const markdownInitial = sowAnalysis?.stepInfo[0]?.infos[0]?.value ?? '';
    const markdownCurrent = markdown;

    return markdownInitial === (form || markdownCurrent);
  };

  const toggleEditMode = () => {
    setIsEditMode(true);
  };

  const handleFinishStep = async (form?: string) => {
    if (!currentStepId) {
      return;
    }

    const payload: StepInfosPayload = {
      stepInfos: [
        {
          order: 0,
          infos: [{ value: form ?? markdown }],
          model: modelAISelected,
        },
      ],
    };

    await mutateAsync({ id: currentStepId, status: EStatusTask.COMPLETED, select: 'all', isGenerate: true, stepIds: [], stepInfoIds: [] });

    await updateQuestionAnswer(payload, currentStepId);

    _setIsSaved(true);

    clearStep(currentStepId);
  };

  const handleSubmit = async () => {
    if (!currentStepId) {
      return;
    }

    if (currentStep.status !== EStatusTask.COMPLETED) {
      handleFinishStep();
      mutateAsync({ id: currentTask?.id ?? '', status: EStatusTask.COMPLETED });
      mutateAsync({ id: currentStep?.id ?? '', status: EStatusTask.COMPLETED });
      updateStatus(currentTask?.id ?? '', EStatusTask.COMPLETED, true);
    }

    clearStep(currentStep?.id ?? '');

    completeStep(currentStepId);
  };

  const handleChangeEditor = (data: EditorContentChanged) => {
    const { markdown } = data;
    setForm(markdown);

    if (!currentStepId) {
      return;
    }
    const isChanged = compareMarkdown(markdown);

    _setIsSaved(isChanged);
    registerStep(currentStepId, () => !isChanged);
  };

  const discardChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setTitlePopUp(titleUnSave);
      setDescriptionPopUp(descriptionUnSave);
      setIsClickUnSaved(true);
      setIsShowModal(true);
      return;
    }

    clearStep(currentStep?.id ?? '');

    setForm(markdown);
    setIsEditMode(false);
  };

  const confirmChange = () => {
    if (!currentStep) {
      return;
    }
    const isChanged = compareMarkdown(form);
    if (!isChanged && currentStep.status === EStatusTask.COMPLETED) {
      setIsClickUnSaved(false);
      setTitlePopUp(titleConfirm);
      setDescriptionPopUp(descriptionConfirm);
      setIsShowModal(true);
      return;
    }
    clearStep(currentStep?.id ?? '');

    setMarkdown(form);
    setIsEditMode(false);
  };

  const handleConfirmPopUp = () => {
    if (isClickUnSaved) {
      setForm(markdown);
      setIsEditMode(false);
      setIsShowModal(false);
      return;
    }
    clearStep(currentStep?.id ?? '');

    handleFinishStep(form);
    setMarkdown(form);
    setIsEditMode(false);
    setIsShowModal(false);
  };

  const handleCancelPopUp = () => {
    setIsShowModal(false);
  };

  const handleReGen = async () => {
    if (!currentStepId) {
      return;
    }
    setIsLoading(true);
    setMarkdown('');
    setForm('');

    updateStatus(currentStepId, EStatusTask.IN_PROGRESS);
    updateStatus(currentTask?.id ?? '', EStatusTask.IN_PROGRESS, true);

    const previousStep = sowAnalysis?.stepInfoPrevious[0]?.stepId ?? '';

    const payloadPreviousStep = sowAnalysis?.stepInfoPrevious[0]!.infos;

    await mutateAsync({
      id: currentStepId,
      status: EStatusTask.IN_PROGRESS,
      select: 'all',
      stepIds: [currentStepId],
      stepInfoIds: [],
      stepInfoIdsGenerate: [],
    });

    await updateQuestionAnswer({ stepInfos: [{
      infos: [...payloadPreviousStep!],
      order: 0,
      model: modelAISelected,
    }] }, previousStep);
  };

  const handleDownloadFile = async () => {
    if (!currentStep) {
      return;
    }
    const nameStep = currentStep.name;
    const html = await markdownToHTMLToDocFile(markdown);
    await downloadMDToFile(html, projectName, nameStep);
  };

  const isChangedModel = useMemo(() => {
    return modelAIDefault !== modelAISelected;
  }, [modelAIDefault, modelAISelected]);

  const handleChangeModelAI = (data: string) => {
    setModelAISelected(data);
  };

  return isLoading
    ? (
        <div className="p-4 md:p-6 ">
          <div className="mb-1 md:mb-2">{t('common.analyzing')}</div>
          <ProjectCardSkeleton />
          <MessageWarning />

        </div>
      )
    : (
        <div className="p-4 md:p-6">

          <div className="flex items-center gap-1.5 justify-end sticky mt-[-60px] top-4 right-4 md:right-6 md:top-6 z-1">
            {isEditMode
              ? (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={discardChange}
                    >
                      {t('common.discard')}
                    </Button>

                    <Button
                      type="button"
                      onClick={confirmChange}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      {t('common.confirm')}
                    </Button>
                  </>
                )
              : (
                  <>
                    {/* {isShowButtonReGen && (
                      <Button onClick={handleReGen} type="button" variant="outline" className="text-cyan-500 bg-cyan-50">
                        <RefreshCcw className="h-5 w-5 " />
                      </Button>
                    )} */}
                    {isShowEditButton && (
                      <Button
                        type="button"
                        variant="outline"
                        onClick={toggleEditMode}
                      >
                        <FileEditIcon className="h-5 w-5 " />
                        {t('common.edit')}
                      </Button>
                    )}

                    <Button
                      type="button"
                      variant="outline"
                      onClick={handleDownloadFile}
                    >
                      <ArrowDownTrayIcon className="h-5 w-5 " />
                    </Button>
                    <Button
                      type="button"
                      onClick={handleSubmit}
                    >
                      <CheckBadgeIcon className="h-5 w-5 " />
                      {t('common.approve')}
                    </Button>
                  </>
                )}

          </div>

          <SelectModelAI
            onChangeModel={handleChangeModelAI}
            defaultValue={modelAIDefault}
            disable={false}
            top="top-55"
            right="right-10"
            isShowReGenButton={(isShowButtonReGen || isChangedModel) && !isEditMode}
            onReGen={handleReGen}
          />

          <div className="mt-6">
            {
              isEditMode
                ? <Editor onChange={handleChangeEditor} value={markdown} />

                : <MarkdownRenderer content={markdown} />
            }
          </div>

          {/* Modal for confirm content */}
          <GuardConfirmationModal
            open={isShowModal}
            onOpenChange={() => {}}
            title={titlePopup}
            description={descriptionPopUp}
            onConfirm={() => handleConfirmPopUp()}
            onCancel={() => handleCancelPopUp()}
            confirmText={t('common.continue')}
            cancelText={t('common.cancel')}
          />

          {/* Modal for guard */}
          <GuardConfirmationModal
            open={showDialog}
            onOpenChange={() => {}}
            title={title}
            description={message}
            onConfirm={onConfirm}
            onCancel={onCancel}
          />
        </div>
      );
};

export default ProjectScopingGeneration;
