'use client';
import { CopilotChat } from '@copilotkit/react-ui';
import React from 'react';
import { useChatBoxVisible } from '@/features/project-management/stores/chatbox-store';
import { AssistantAvatar, UserAvatar } from './ChatAvatar';
import { useTranslations } from 'next-intl';

/**
 * ChatBox component that displays a chat interface
 *
 * The visibility is controlled by the useChatBoxVisibility hook
 */
export function ChatBox() {
  const t = useTranslations('workflow');
  // Get the visibility state from the custom hook
  const isVisible = useChatBoxVisible();

  // If not visible, don't render the component
  if (!isVisible) {
    return null;
  }

  return (
    <CopilotChat
      className=""
      instructions={t('AI.instructions')}
      labels={{
        title: t('AI.title'),
        initial: t('AI.initial'),
      }}
      UserMessage={UserAvatar}
      AssistantMessage={AssistantAvatar}
    />
  );
}
